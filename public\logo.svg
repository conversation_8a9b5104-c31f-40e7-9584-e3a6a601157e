<svg width="226" height="155" viewBox="0 0 226 155" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_46_50)">
<path d="M218.473 1.08008L225.305 9.28125L225.144 13.3008L194.825 44.2744C224.46 85.6299 194.014 140.702 147.752 139.278L144.845 136.28V127.395L147.551 124.409C167.342 122.462 180.724 111.499 186.682 97.6719C192.243 84.7637 191.422 69.1623 182.978 55.624C181.554 57.124 179.593 59.1862 177.225 61.6611C172.113 67.0016 165.104 74.271 157.514 81.9834C142.407 97.3333 124.775 114.67 115.313 121.882C105.794 129.138 97.5711 132.935 90.4521 134.704C83.3135 136.478 77.4459 136.173 72.7275 135.447C63.7943 134.072 54.6418 131.748 42.1924 123.227L11.9766 154.099L7.52734 153.92L0.695312 145.719L0.884766 141.671L31.3828 111.353C16.8268 94.6508 15.535 70.7909 23.4756 52.3896C32.8259 30.7218 53.6807 16.4035 77.4717 16.4033L80.4717 19.4033V29.6562L77.3613 32.6543C43.8868 31.414 23.2216 71.2189 42.5479 100.08C43.7819 98.7724 45.4277 97.0341 47.4043 94.959C51.9977 90.1366 58.38 83.4935 65.5342 76.2109C79.811 61.6778 97.2706 44.4871 109.721 34.1797C134.725 13.4791 163.147 17.3776 183.128 33.0947L214.003 0.922852L218.473 1.08008ZM146.777 37.0254C144.89 36.9651 143.026 37.0109 141.209 37.1602C133.279 37.8475 126.249 40.4663 121.954 44.7627L121.911 44.8047L118.865 47.7324L54.5332 110.235C57.2141 112.17 60.1887 113.741 63.3389 114.951C68.1888 116.735 73.4332 117.689 78.6553 117.816C88.643 118.06 98.1017 115.279 104.225 110.072C110.561 104.684 117.487 98.3504 124.435 91.7539C133.012 83.5202 141.616 74.8851 149.149 67.1641C155.952 60.1921 161.865 53.983 166.078 49.5176C167.982 47.4994 169.538 45.8366 170.671 44.6221C164.547 39.9715 155.722 37.3108 146.777 37.0254Z" fill="url(#paint0_linear_46_50)"/>
</g>
<defs>
<filter id="filter0_i_46_50" x="0.695312" y="0.922852" width="224.609" height="154.176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_46_50"/>
</filter>
<linearGradient id="paint0_linear_46_50" x1="225.305" y1="77.5107" x2="0.695313" y2="77.5107" gradientUnits="userSpaceOnUse">
<stop stop-color="#816DE2"/>
<stop offset="1" stop-color="#85BCD1"/>
</linearGradient>
</defs>
</svg>
