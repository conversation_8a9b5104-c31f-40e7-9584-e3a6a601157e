{"$schema": "https://schema.tauri.app/config/2", "productName": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "identifier": "com.kamjin.chatless", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:3000", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"label": "main", "title": "<PERSON><PERSON><PERSON>", "width": 1024, "height": 768, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "closable": true, "backgroundColor": "#0f172a"}], "security": {"csp": null, "capabilities": ["main-capability"]}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["onnxruntime.dll", "libonnxruntime.dylib"]}, "plugins": {"store": null, "sql": {"enabled": true, "drivers": ["sqlite"], "preload": ["sqlite:mychat.db"]}, "http": null, "opener": null, "dialog": null}}