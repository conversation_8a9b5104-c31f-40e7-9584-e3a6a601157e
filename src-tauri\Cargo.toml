[package]
name = "chatless"
version = "0.1.0"
description = "Chatless - A modern chat application"
authors = ["you"]
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "chatless_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
serde_json = "1"
serde = { version = "1", features = ["derive"] }
tauri = { version = "2", features = ["tray-icon"] }
tauri-plugin-opener = "2"
tauri-plugin-sql = { version = "2", features = ["sqlite"] }
tauri-plugin-fs = "2"
tauri-plugin-http = { version = "2.4.3", features = ["dangerous-settings", "cookies", "unsafe-headers"] }
tauri-plugin-store = "2"
tauri-plugin-upload = "2"
tauri-plugin-dialog = "2"
reqwest = { version = "0.12", features = ["stream"] }
futures-util = "0.3"
tokio = { version = "1", features = ["full"] }
lazy_static = "1"


# —— 嵌入推理相关 ——
ort = { version = "2.0.0-rc.10", features = [ "load-dynamic" ] }
tokenizers = { version = "0.19.1", default-features = false, features = ["onig"] }
ndarray = { version = "0.15", features = ["serde"] }
ndarray-stats = "0.5.1"
once_cell = "1.19.0"
rayon = "1.10.0"
# Command line interface helper
clap = { version = "4", features = ["derive"] }


# 文档解析依赖 - 所有平台通用
anyhow = "1.0"
pdf-extract = "0.7.0"
docx-rust = "0.1.10"
pulldown-cmark = "0.9.3"
crc32fast = "1.4.0"
tauri-plugin-log = "2"
log = "0.4"

# 仅在Windows平台添加windows依赖
[target.'cfg(target_os = "windows")'.dependencies]
# 锁定windows版本以解决冲突
windows = "=0.61.1"
webview2-com-sys = "=0.37.0"

# 使用patch仅在Windows平台修正冲突
[target.'cfg(target_os = "windows")'.patch.crates-io]
webview2-com-sys = "=0.37.0"

[features]
# by default Tauri runs in production mode
# when `tauri dev` runs it is executed with `cargo run --no-default-features` if `devPath` is an URL
default = [ "custom-protocol" ]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = [ "tauri/custom-protocol" ]
