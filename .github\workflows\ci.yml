name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Select test type. "basic" for fast checks, "full" for all, "tauri-only" for build tests.'
        required: true
        default: 'basic'
        type: choice
        options:
        - basic
        - full
        - tauri-only
      platforms:
        description: 'Select platforms to build (only for "full" or "tauri-only" types).'
        required: false
        type: choice
        default: 'all'
        options:
        - all
        - ubuntu
        - windows
        - macos
        - ubuntu-windows
        - ubuntu-macos
        - windows-macos
      upload_artifacts:
        description: 'Upload build artifacts (consumes storage space).'
        required: false
        type: boolean
        default: false

jobs:
  # 作业1：前端快速检查
  frontend-checks:
    # if: github.event_name != 'workflow_dispatch' || (github.event.inputs.test_type == 'basic' || github.event.inputs.test_type == 'full')
    if: false  # ✅ 暂时禁用，后续可恢复上面的 if 条件
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Run linting
        run: pnpm lint
      - name: Type check
        run: pnpm tsc --noEmit
      - name: Build Frontend
        run: pnpm build

  # 作业2：后端快速检查 (cargo check)
  backend-check:
    # if: github.event_name != 'workflow_dispatch' || (github.event.inputs.test_type == 'basic' || github.event.inputs.test_type == 'full')
    if: false  # ✅ 暂时禁用，后续可恢复上面的 if 条件
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
      - name: Run cargo check
        run: cd src-tauri && cargo check

  # --- 手动触发的完整构建测试 ---

  test-tauri-ubuntu:
    needs: [frontend-checks, backend-check]
    # if: >-
    #   always() &&
    #   github.event_name == 'workflow_dispatch' &&
    #   (github.event.inputs.test_type == 'tauri-only' || github.event.inputs.test_type == 'full') &&
    #   (contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'ubuntu'))
    if: false  # ✅ 暂时禁用，后续可恢复上面的 if 条件
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Install Rust system dependencies
        run: sudo apt-get update && sudo apt-get install -y libwebkit2gtk-4.0-dev build-essential curl wget file libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
      - name: Build Tauri app (Debug)
        run: pnpm tauri build --debug
      - name: Upload build artifacts (if requested)
        if: ${{ github.event.inputs.upload_artifacts }}
        uses: actions/upload-artifact@v4
        with:
          name: chatless-debug-ubuntu
          path: src-tauri/target/debug/bundle/

  test-tauri-windows:
    needs: [frontend-checks, backend-check]
    # if: >-
    #   always() &&
    #   github.event_name == 'workflow_dispatch' &&
    #   (github.event.inputs.test_type == 'tauri-only' || github.event.inputs.test_type == 'full') &&
    #   (contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'windows'))
    if: false  # ✅ 暂时禁用，后续可恢复上面的 if 条件
    runs-on: windows-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Build Tauri app (Debug)
        run: pnpm tauri build --debug
      - name: Upload build artifacts (if requested)
        if: ${{ github.event.inputs.upload_artifacts }}
        uses: actions/upload-artifact@v4
        with:
          name: chatless-debug-windows
          path: src-tauri/target/debug/bundle/

  test-tauri-macos:
    needs: [frontend-checks, backend-check]
    # if: >-
    #   always() &&
    #   github.event_name == 'workflow_dispatch' &&
    #   (github.event.inputs.test_type == 'tauri-only' || github.event.inputs.test_type == 'full') &&
    #   (contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'macos'))
    if: false  # ✅ 暂时禁用，后续可恢复上面的 if 条件
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Build Tauri app (Debug)
        run: pnpm tauri build --debug
      - name: Upload build artifacts (if requested)
        if: ${{ github.event.inputs.upload_artifacts }}
        uses: actions/upload-artifact@v4
        with:
          name: chatless-debug-macos
          path: src-tauri/target/debug/bundle/