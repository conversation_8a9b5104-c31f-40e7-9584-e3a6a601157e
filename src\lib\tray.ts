import { TrayIcon } from '@tauri-apps/api/tray';
import { Menu } from '@tauri-apps/api/menu';
import { PredefinedMenuItem } from '@tauri-apps/api/menu';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { defaultWindowIcon } from '@tauri-apps/api/app';

// 系统托盘管理类
class SystemTrayManager {
  private tray: TrayIcon | null = null;
  private isInitialized = false;

  // 初始化系统托盘
  async initialize() {
    if (this.isInitialized) return;

    try {
      // 创建真正的分割线
      const separator1 = await PredefinedMenuItem.new({ item: 'Separator' });
      const separator2 = await PredefinedMenuItem.new({ item: 'Separator' });

      // 创建菜单项并直接绑定事件处理器
      const menu = await Menu.new({
        items: [
          {
            id: 'new_chat',
            text: '新建聊天',
            action: () => this.createNewChat()
          },
          separator1,
          {
            id: 'minimize',
            text: '最小化到托盘',
            action: () => this.minimizeToTray()
          },
          separator2,
          {
            id: 'settings',
            text: '设置',
            action: () => this.navigateTo('settings')
          },
          {
            id: 'quit',
            text: '退出',
            action: () => this.quit()
          }
        ]
      });

      // 获取应用图标
      const icon = await defaultWindowIcon();

      // 创建系统托盘
      this.tray = await TrayIcon.new({
        tooltip: 'Chatless',
        icon: icon || 'icons/icon.ico', // 提供默认图标
        menu,
        menuOnLeftClick: false, // 左键点击不显示菜单
        action: (event) => {
          // 处理托盘图标点击事件
          if (event.type === 'Click' && event.button === 'Left' && event.buttonState === 'Up') {
            this.toggleWindow();
          }
        }
      });

      this.isInitialized = true;
      console.log('系统托盘初始化成功');
    } catch (error) {
      console.error('系统托盘初始化失败:', error);
    }
  }

  // 显示窗口
  private async showWindow() {
    try {
      const window = getCurrentWindow();
      await window.show();
      await window.setFocus();
    } catch (error) {
      console.error('显示窗口失败:', error);
    }
  }

  // 隐藏窗口
  private async hideWindow() {
    try {
      const window = getCurrentWindow();
      await window.hide();
    } catch (error) {
      console.error('隐藏窗口失败:', error);
    }
  }

  // 切换窗口显示/隐藏
  private async toggleWindow() {
    try {
      const window = getCurrentWindow();
      const isVisible = await window.isVisible();
      
      if (isVisible) {
        await window.hide();
      } else {
        await window.show();
        await window.setFocus();
      }
    } catch (error) {
      console.error('切换窗口状态失败:', error);
    }
  }

  // 确保窗口显示并获取焦点
  private async ensureWindowVisible() {
    try {
      const window = getCurrentWindow();
      await window.show();
      await window.setFocus();
    } catch (error) {
      console.error('显示窗口失败:', error);
    }
  }

  // 导航到指定页面
  private async navigateTo(page: string) {
    try {
      // 确保窗口显示
      await this.ensureWindowVisible();
      
      const window = getCurrentWindow();
      // 发送导航事件到前端
      await window.emit('navigate', page);
    } catch (error) {
      console.error(`导航到${page}失败:`, error);
    }
  }

  // 创建新聊天会话
  private async createNewChat() {
    try {
      // 确保窗口显示
      await this.ensureWindowVisible();
      
      const window = getCurrentWindow();
      // 发送创建新聊天事件到前端
      await window.emit('create-new-chat');
    } catch (error) {
      console.error('创建新聊天失败:', error);
    }
  }

  // 最小化到托盘
  private async minimizeToTray() {
    try {
      const window = getCurrentWindow();
      await window.hide();
    } catch (error) {
      console.error('最小化到托盘失败:', error);
    }
  }

  // 退出应用
  private async quit() {
    try {
      this.tray?.close();

      // 使用 Tauri 的 exit 命令
      const { invoke } = await import('@tauri-apps/api/core');
      await invoke('exit', { code: 0 });
    } catch (error) {
      console.error('退出应用失败:', error);
    }
  }

  // 设置托盘图标
  async setIcon(iconPath: string) {
    if (!this.tray || !this.isInitialized) return;

    try {
      await this.tray.setIcon(iconPath);
    } catch (error) {
      console.error('设置托盘图标失败:', error);
    }
  }

  // 设置托盘工具提示
  async setTooltip(tooltip: string) {
    if (!this.tray || !this.isInitialized) return;

    try {
      await this.tray.setTooltip(tooltip);
    } catch (error) {
      console.error('设置托盘工具提示失败:', error);
    }
  }
}

// 创建全局实例
const trayManager = new SystemTrayManager();

// 导出实例和类
export { trayManager, SystemTrayManager }; 