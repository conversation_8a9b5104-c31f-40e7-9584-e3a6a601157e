---
name: Bug 报告
about: 创建一个 bug 报告以帮助我们改进
title: '[BUG] '
labels: ['bug']
assignees: ''
---

## 🐛 问题描述

请简要描述这个 bug。

## 🔄 重现步骤

重现该行为的步骤：
1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## ✅ 预期行为

请描述您期望发生的事情。

## ❌ 实际行为

请描述实际发生的事情。

## 💻 环境信息

**操作系统：**
- [ ] Windows
- [ ] macOS
- [ ] Linux

**版本：**
- chatless 版本：[例如 0.1.0]
- 操作系统版本：[例如 Windows 11 22H2]
- Node.js 版本：[例如 18.17.0]
- Rust 版本：[例如 1.70.0]

**浏览器（如果适用）：**
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge

## 📸 截图（可选）

如果适用，添加截图以帮助解释您的问题。

## 📋 检查清单

- [ ] 我已经搜索了现有的 issues，确认这是一个新问题
- [ ] 我已经尝试了基本的故障排除步骤
- [ ] 我提供了所有相关的环境信息 