# 更新日志

本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范。

## [未发布]

### 新增
- 初始版本发布

### 修复
- 修复了多个已知问题

### 变更
- 项目开源准备完成

## [0.1.0] - 2025-01-XX

### 新增
- 🎉 初始版本发布
- 💬 多AI提供商支持（OpenAI、Anthropic、DeepSeek等）
- 🏠 本地AI模型支持（Ollama集成）
- 📚 知识库功能（文档上传和检索）
- 🔒 隐私优先设计（本地数据存储）
- 🖥️ 跨平台支持（Windows、macOS、Linux）
- 🎨 现代UI界面（Next.js 15 + TailwindCSS 4）
- 📊 聊天历史管理
- ⚙️ 丰富的设置选项
- 🔍 智能文档检索
- 📝 提示词管理
- 📈 使用统计和分析

### 技术特性
- 基于 Tauri 2.0 的桌面应用框架
- Next.js 15 前端框架
- TypeScript 类型安全
- SQLite 本地数据库
- 向量存储和检索系统
- 文档解析支持（PDF、Word、Markdown、文本）

### 安全特性
- 本地数据存储，无云端上传
- API密钥本地加密存储
- 隐私保护设计
- 开源透明代码

---

## 版本说明

### 版本号格式
我们使用语义化版本控制：`主版本.次版本.修订版本`

- **主版本**：不兼容的 API 修改
- **次版本**：向下兼容的功能性新增
- **修订版本**：向下兼容的问题修正

### 更新类型

- 🎉 **新增**：新功能
- 🔧 **修复**：Bug修复
- ⚡ **优化**：性能优化
- 🔄 **变更**：功能变更
- 🗑️ **移除**：功能移除
- 🔒 **安全**：安全相关更新
- 📚 **文档**：文档更新

---

## 贡献

如果您想为更新日志做出贡献，请：

1. 遵循现有的格式和风格
2. 使用清晰的描述
3. 按时间倒序排列
4. 包含相关的 issue 或 PR 链接

---

## 更多信息

- [GitHub Releases](https://github.com/kamjin3086/chatless/releases)
- [项目主页](https://github.com/kamjin3086/chatless)
- [问题反馈](https://github.com/kamjin3086/chatless/issues) 