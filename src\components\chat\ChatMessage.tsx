"use client";

import { useState, useMemo, useEffect } from 'react';
import { <PERSON><PERSON>, Star, RefreshCcw } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from '@/components/ui/button';
import { ContextMenu, createMessageMenuItems } from '@/components/ui/context-menu';
import { UserMessageBlock } from './UserMessageBlock';
import { AIMessageBlock } from './AIMessageBlock';
import { motion } from 'framer-motion';

// Toggle for verbose internal logging. Set to true when debugging ChatMessage rendering.
const DEBUG_CHAT_MESSAGE = false;

interface ChatMessageProps {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  model?: string;
  onEdit?: (id: string) => void;
  onCopy?: (content: string) => void;
  onStar?: (id: string) => void;
  onRetry?: () => void;
  status: 'pending' | 'sending' | 'sent' | 'error' | 'loading' | 'aborted';
  thinking_duration?: number;
  onSaveThinkingDuration?: (messageId: string, duration: number) => void;
  
  // 新增的文档引用props
  documentReference?: {
    fileName: string;
    fileType: string;
    fileSize: number;
    summary: string;
  };
  contextData?: string;
  
  // 知识库引用props
  knowledgeBaseReference?: {
    id: string;
    name: string;
  };
  images?: string[];
}

const formatTimestamp = (timestamp: string | undefined): string => {
  if (!timestamp) return '';
  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffSeconds = Math.round((now.getTime() - date.getTime()) / 1000);
    const diffMinutes = Math.round(diffSeconds / 60);
    const diffHours = Math.round(diffMinutes / 60);
    const diffDays = Math.round(diffHours / 24);

    if (diffSeconds < 60) return `${diffSeconds}秒前`;
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays === 1) return `昨天 ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    if (diffDays < 7) return `${diffDays}天前`;
    return date.toLocaleDateString();
  } catch (e) {
    console.error("Error formatting timestamp:", e);
    return timestamp;
  }
};

export function ChatMessage({
  id,
  content,
  role,
  timestamp,
  model,
  onEdit,
  onCopy,
  onStar,
  onRetry,
  status,
  thinking_duration,
  onSaveThinkingDuration,
  
  // 新增的文档引用props
  documentReference,
  contextData,
  
  // 知识库引用props
  knowledgeBaseReference,
  images,
}: ChatMessageProps) {
  if (DEBUG_CHAT_MESSAGE) {
    console.log(`[ChatMessage] Rendering message ${id}:`, {
      role,
      hasDocRef: !!documentReference,
      docRef: documentReference,
      hasContextData: !!contextData,
      hasKnowledgeBase: !!knowledgeBaseReference,
      status,
      images: images && images.length > 0 ? `[base64数组省略, 共${images.length}张]` : undefined
    });
  }
  
  // 在组件挂载和更新时记录props变化
  useEffect(() => {
    if (DEBUG_CHAT_MESSAGE) {
      console.log(`[ChatMessage] Props updated for message ${id}:`, {
        documentReference,
        contextData,
        knowledgeBaseReference,
        status,
        images: images && images.length > 0 ? `[base64数组省略, 共${images.length}张]` : undefined
      });
    }
  }, [id, documentReference?.fileName, contextData, knowledgeBaseReference?.id, status, images?.length]);
  

  const isUser = role === "user";
  const formattedTime = formatTimestamp(timestamp);

  const isStreaming = status === 'loading';

  const messageContent = useMemo(() => {
    if (DEBUG_CHAT_MESSAGE) {
      console.log(`[ChatMessage] Preparing message content for ${id}:`, {
        isUser,
        hasDocRef: !!documentReference,
      });
    }
    

    
    return isUser 
      ? <UserMessageBlock 
          id={id}
          content={content}
          documentReference={documentReference}
          contextData={contextData}
          knowledgeBaseReference={knowledgeBaseReference}
          images={images}
          onEdit={onEdit}
          onCopy={onCopy}
        /> 
      : <AIMessageBlock 
          content={content}
          isStreaming={isStreaming}
          thinkingDuration={thinking_duration}
          onStreamingComplete={(duration) => {
            if (onSaveThinkingDuration) {
              onSaveThinkingDuration(id, duration);
            }
          }}
        />;
  }, [content, isUser, documentReference?.fileName, contextData, knowledgeBaseReference?.id, images?.length, onEdit, onCopy, id, isStreaming, thinking_duration, onSaveThinkingDuration]);

  return (
    <div
      className={cn(
        "flex chat-transition group mb-6 relative",
        isUser 
          ? "flex-row-reverse justify-start max-w-[85%] ml-auto" 
          : "max-w-[85%]"
      )}

    >
      {/* 头像隐藏，保留对齐占位 */}
      <div className={cn("w-0 h-0 mr-0 ml-0")}></div>

      {/* 消息内容 */}
      <div className={cn(
        "flex flex-col min-w-0 max-w-full", 
        isUser ? "items-end" : "items-start"
      )}>
        <ContextMenu
          menuItems={createMessageMenuItems(
            id,
            content,
            !isUser,
            onCopy || ((content) => console.log('复制:', content)),
            isUser ? onEdit : undefined,
            !isUser ? onRetry : undefined,
            onStar
          )}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.15 }}
            className={cn(
              "max-w-full min-w-0 transition-all duration-200",
              isUser
                ? "p-0 bg-transparent shadow-none"
                : "p-0 bg-transparent shadow-none",
              !isUser && "rounded-tl-sm"
            )}
          >
            {messageContent}
          </motion.div>
        </ContextMenu>

        {/* 时间戳和模型信息 */}
        {(formattedTime || (!isUser && model)) && (
          <div className={cn(
            "flex items-center text-xs text-slate-500 dark:text-slate-400 mt-1.5",
            isUser ? "self-end" : "self-start w-full"
          )}>
            <div className="flex items-center gap-2">
              {!isUser && model && (
                <span className="font-medium">{model}</span>
              )}
              <span>{formattedTime}</span>
            </div>
            
            {/* AI消息功能按钮 */}
            {!isUser && (
              <div className="flex items-center gap-1 ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none group-hover:pointer-events-auto">
                {onRetry && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onRetry}
                    className="h-5 w-5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
                    title="重试"
                  >
                    <RefreshCcw className="w-3 h-3" />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => (onCopy ? onCopy(content) : navigator.clipboard.writeText(content))}
                  className="h-5 w-5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
                  title="复制"
                >
                  <Copy className="w-3 h-3" />
                </Button>
                {onStar && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onStar(id)}
                    className="h-5 w-5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
                    title="收藏回答"
                  >
                    <Star className="w-3 h-3" />
                  </Button>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
} 