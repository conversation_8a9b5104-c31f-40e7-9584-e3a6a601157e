{"name": "chatless", "private": false, "version": "0.1.0", "license": "MIT", "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "cross-env NODE_ENV=production NODE_OPTIONS=\"--max_old_space_size=8192 --max_semi_space_size=256\" next build", "start": "next start", "lint": "biome check src/ && next lint", "test": "echo \"No tests specified yet\" && exit 0", "test:watch": "echo \"No tests specified yet\" && exit 0", "tauri": "tauri", "generate:settings-index": "node scripts/generate-settings-index.cjs", "update:app-info": "node scripts/update-app-info.js", "prebuild": "pnpm run generate:settings-index && pnpm run update:app-info", "pretauri": "pnpm run generate:settings-index && pnpm run update:app-info", "dev:db:reset": "tsx src/scripts/dev-db-reset.ts", "dev:test:providers": "tsx src/scripts/test-providers.ts"}, "dependencies": {"@fontsource/inter": "^5.2.6", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.2.4", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-fs": "^2.2.1", "@tauri-apps/plugin-http": "^2.4.3", "@tauri-apps/plugin-log": "~2", "@tauri-apps/plugin-opener": "^2.2.6", "@tauri-apps/plugin-sql": "^2.2.0", "@tauri-apps/plugin-store": "~2", "@tauri-apps/plugin-upload": "^2.2.2", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "immer": "^10.1.1", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "mammoth": "^1.9.1", "next": "^15.3.1", "next-themes": "^0.4.6", "pdf-parse": "^1.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.9", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "shiki": "^3.3.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.25.0", "@next/eslint-plugin-next": "^15.3.1", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/cli": "^2", "@types/lodash": "^4.17.16", "@types/node": "^22.14.1", "@types/pdf-parse": "^1.1.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "@vitejs/plugin-react": "^4.4.1", "cross-env": "^7.0.3", "eslint": "^9.21.0", "eslint-config-next": "^15.3.1", "eslint-plugin-react-hooks": "^5.2.0", "tailwindcss": "^4.1.4", "tsx": "^4.19.1", "tw-animate-css": "^1.2.8", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1"}}