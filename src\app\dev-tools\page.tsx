'use client';

import { useEffect, useState } from 'react';
import { DevToolsPanel } from '@/components/dev/DevToolsPanel';
import SampleDataManager from '@/components/dev/SampleDataManager';
import { DatabaseRepairTool } from '@/components/dev/DatabaseRepairTool';
import { PerformanceMonitor } from '@/components/dev/PerformanceMonitor';
import { getDevToolsStatus } from '../../lib/utils/environment';
import { Button } from '@/components/ui/button';
import { Loader2, RotateCcw, RefreshCw, AlertTriangle, Database, FileText, Trash2, Download, Sparkles, ChevronDown, ChevronUp, Settings } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { SampleDataInitializer, cleanupDuplicateDocuments } from "@/lib/sampleDataInitializer";
import { specializedStorage } from "@/lib/storage";
import { GoogleAIProvider } from "@/lib/llm/providers/GoogleAIProvider";

interface DevToolsStatus {
  isDevEnv: boolean;
  isTauriApp: boolean;
  canUseDevTools: boolean;
}

export default function DevToolsPage() {
  const [status, setStatus] = useState<DevToolsStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [isResetting, setIsResetting] = useState(false);
  const [isReinitializing, setIsReinitializing] = useState(false);
  const [isCleaning, setIsCleaning] = useState(false);
  const [isCheckingLock, setIsCheckingLock] = useState(false);
  const [lockStatus, setLockStatus] = useState<{
    hasLock: boolean;
    isExpired: boolean;
    lockTime?: string;
  } | null>(null);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState("");
  const [message, setMessage] = useState("");
  const [showAdvancedTools, setShowAdvancedTools] = useState(false);
  const [googleTestResult, setGoogleTestResult] = useState<string>('');
  const [isTestingGoogle, setIsTestingGoogle] = useState(false);

  const handleResetSampleData = async () => {
    setIsResetting(true);
    try {
      const { SampleDataInitializer } = await import('@/lib/sampleDataInitializer');
      await SampleDataInitializer.fullReset({
        onProgress: (step, progress) => {
          console.log(`${step}: ${progress}%`);
        }
      });
      alert('示例数据重置完成');
    } catch (error) {
      console.error('重置失败:', error);
      alert('重置失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsResetting(false);
    }
  };

  const handleFullReset = async () => {
    if (!confirm("⚠️ 这将删除所有数据（数据库、文件、配置）确定要继续吗？")) {
      return;
    }
    
    if (!confirm("⚠️ 最后确认：这是不可逆的操作，将删除所有数据")) {
      return;
    }

    setIsResetting(true);
    setProgress(0);
    setCurrentStep("开始重置...");
    setMessage("");

    try {
      await SampleDataInitializer.fullReset({
        onProgress: (step, prog) => {
          setCurrentStep(step);
          setProgress(prog);
        }
      });
      
      setMessage("完全重置成功");
      
      // 2秒后重新加载页面
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      console.error('重置失败:', error);
      setMessage(`❌ 重置失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsResetting(false);
      setProgress(100);
    }
  };

  const handleReinitialize = async () => {
    if (!confirm("确定要重新初始化示例数据吗？这将重新创建所有示例知识库和文档。")) {
      return;
    }

    setIsReinitializing(true);
    setProgress(0);
    setCurrentStep("开始重新初始化...");
    setMessage("");

    try {
      await SampleDataInitializer.initializeAll({
        onProgress: (step, prog) => {
          setCurrentStep(step);
          setProgress(prog);
        },
        overrideExisting: true
      });
      
      setMessage("示例数据重新初始化成功");

    } catch (error) {
      console.error('重新初始化失败:', error);
      setMessage(`❌ 重新初始化失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsReinitializing(false);
      setProgress(100);
    }
  };

  const handleCleanupDuplicates = async () => {
    if (!confirm("确定要强制清理重复的知识库和文档吗？这将彻底删除所有重复项目，只保留最早创建的版本。")) {
      return;
    }

    setIsCleaning(true);
    setProgress(0);
    setCurrentStep("开始强制清理重复数据...");
    setMessage("");

    try {
      // 使用新的强制清理方法
      await SampleDataInitializer.forceCleanupAllDuplicates({
        onProgress: (step, prog) => {
          setCurrentStep(step);
          setProgress(prog);
        }
      });
      
      setMessage(`强制清理完成所有重复数据已清除`);

    } catch (error) {
      console.error('强制清理失败:', error);
      setMessage(`❌ 强制清理失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsCleaning(false);
    }
  };

  const handleCheckLock = async () => {
    setIsCheckingLock(true);
    try {
      const lock = await specializedStorage.sampleData.getLock();
      const isExpired = await specializedStorage.sampleData.isLockExpired();
      
      setLockStatus({
        hasLock: !!lock,
        isExpired,
        lockTime: lock ? new Date(parseInt(lock)).toLocaleString() : undefined
      });
      
      if (lock) {
        setMessage(`🔍 发现初始化锁: ${isExpired ? '已过期' : '有效'}, 创建时间: ${new Date(parseInt(lock)).toLocaleString()}`);
      } else {
        setMessage('未发现初始化锁');
      }
    } catch (error) {
      console.error('检查锁状态失败:', error);
      setMessage(`❌ 检查锁状态失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsCheckingLock(false);
    }
  };

  const handleClearLock = async () => {
    try {
      await specializedStorage.sampleData.forceClearLock();
      setMessage('初始化锁已清除');
      setLockStatus(null);
    } catch (error) {
      console.error('清除锁失败:', error);
      setMessage(`❌ 清除锁失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const handleTestGoogleAI = async () => {
    setIsTestingGoogle(true);
    setGoogleTestResult('');
    
    try {
      // 创建Google AI Provider实例
      const provider = new GoogleAIProvider('https://generativelanguage.googleapis.com/v1beta');
      
      // 设置API密钥（这里需要用户输入）
      const apiKey = prompt('请输入Google AI API密钥:');
      if (!apiKey) {
        setGoogleTestResult('❌ 未提供API密钥');
        return;
      }
      
      // 设置API密钥到KeyManager
      const { KeyManager } = await import('@/lib/llm/KeyManager');
      await KeyManager.setProviderKey('Google AI', apiKey);
      
      // 测试连接
      setGoogleTestResult('🔍 测试连接中...');
      const connectionResult = await provider.checkConnection();
      
      if (!connectionResult.success) {
        setGoogleTestResult(`❌ 连接失败: ${connectionResult.message}`);
        return;
      }
      
      setGoogleTestResult('✅ 连接成功开始测试流式响应...');
      
      // 测试流式响应
      let streamedContent = '';
      await provider.chatStream(
        'gemini-2.5-flash',
        [{ role: 'user', content: '请用一句话介绍自己' }],
        {
          onStart: () => {
            setGoogleTestResult('🚀 流式响应开始...');
          },
          onToken: (token) => {
            streamedContent += token;
            setGoogleTestResult(`📝 流式内容: ${streamedContent}`);
          },
          onComplete: () => {
            setGoogleTestResult(`✅ 流式响应完成最终内容: ${streamedContent}`);
          },
          onError: (error) => {
            setGoogleTestResult(`❌ 流式响应错误: ${error.message}`);
          }
        }
      );
      
    } catch (error) {
      setGoogleTestResult(`❌ 测试失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsTestingGoogle(false);
    }
  };

  useEffect(() => {
    const checkEnvironment = async () => {
      try {
        const envStatus = await getDevToolsStatus();
        setStatus(envStatus);
      } catch (error) {
        console.error('环境检测失败:', error);
        setStatus({
          isDevEnv: false,
          isTauriApp: false,
          canUseDevTools: false
        });
      } finally {
        setLoading(false);
      }
    };

    checkEnvironment();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold">开发工具</h1>
          <p className="text-muted-foreground mt-2">正在检测环境...</p>
        </div>
      </div>
    );
  }

  if (!status?.canUseDevTools) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">开发工具</h1>
          <p className="text-muted-foreground">
            数据库管理和开发辅助工具
          </p>
        </div>
        
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6 mb-6">
          <div className="flex items-start gap-3">
            <div className="text-yellow-600 dark:text-yellow-400 text-xl">⚠️</div>
            <div>
              <h2 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                环境限制
              </h2>
              <p className="text-yellow-700 dark:text-yellow-300 mb-3">
                开发工具需要在Tauri应用的开发环境中运行。
              </p>
              <div className="bg-yellow-100 dark:bg-yellow-900/40 rounded p-3 mb-3">
                <div className="text-sm space-y-1">
                  <div><strong>开发环境:</strong> {status?.isDevEnv ? '是' : '否'}</div>
                  <div><strong>Tauri应用:</strong> {status?.isTauriApp ? '是' : '否'}</div>
                  {!status?.isDevEnv && <div className="text-orange-600">⚠️ 当前为生产环境，开发工具不可用</div>}
                  {!status?.isTauriApp && <div className="text-orange-600">⚠️ 未检测到Tauri应用环境</div>}
                </div>
              </div>
              <div className="space-y-2 text-sm">
                <p className="font-medium">要使用开发工具，请确保：</p>
                <ol className="list-decimal list-inside space-y-1 text-yellow-700 dark:text-yellow-300">
                  <li>应用在开发模式下运行：<code className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">pnpm tauri dev</code></li>
                  <li>在Tauri应用中访问（而非独立浏览器）</li>
                  <li>或者使用终端命令进行数据库操作（见下方）</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-8 p-4 bg-muted rounded-lg">
          <h2 className="text-lg font-semibold mb-2">替代方案：终端命令</h2>
          <p className="text-sm text-muted-foreground mb-2">
            你可以在终端中使用以下命令进行数据库操作：
          </p>
          <div className="space-y-1 font-mono text-sm bg-black text-green-400 p-3 rounded">
            <div><span className="text-gray-400">#</span> 完全重置数据库</div>
            <div>pnpm dev:db:reset</div>
            <div className="mt-2"><span className="text-gray-400">#</span> 重置并添加测试数据</div>
            <div>pnpm dev:db:reset-test</div>
            <div className="mt-2"><span className="text-gray-400">#</span> 清理数据（保留表结构）</div>
            <div>pnpm dev:db:clear</div>
            <div className="mt-2"><span className="text-gray-400">#</span> 显示开发工具菜单</div>
            <div>pnpm dev:db:menu</div>
          </div>
        </div>
      </div>
    );
  }

  const isAnyOperationRunning = isResetting || isReinitializing || isCleaning || isCheckingLock;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center space-x-2">
        <Database className="h-6 w-6" />
        <h1 className="text-2xl font-bold">开发工具</h1>
        <Badge variant="outline">仅用于开发和测试</Badge>
      </div>

      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>警告：</strong>这些工具仅用于开发和测试环境。在生产环境中使用前请确保已备份所有重要数据。
        </AlertDescription>
      </Alert>

      {/* 主要开发工具面板 */}
      <DevToolsPanel />

      {/* 示例数据管理器 */}
      <SampleDataManager />

      {/* 数据库修复工具 */}
      <DatabaseRepairTool />

      {/* 高级工具切换 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <CardTitle>高级工具</CardTitle>
              <Badge variant="secondary">扩展功能</Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvancedTools(!showAdvancedTools)}
            >
              {showAdvancedTools ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
          <CardDescription>
            {showAdvancedTools ? '隐藏' : '显示'}额外的数据管理和锁管理工具
          </CardDescription>
        </CardHeader>

        {showAdvancedTools && (
          <CardContent>
            {/* 进度显示 */}
            {isAnyOperationRunning && (
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <RotateCcw className="h-4 w-4 animate-spin" />
                    <span>操作进行中...</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm text-muted-foreground mb-2">
                      <span>{currentStep}</span>
                      <span>{progress}%</span>
                    </div>
                    <Progress value={progress} className="w-full" />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 操作结果显示 */}
            {message && !isAnyOperationRunning && (
              <Alert className={`mb-6 ${message.startsWith('✅') ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                <AlertDescription>{message}</AlertDescription>
              </Alert>
            )}

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {/* 数据清理工具 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Sparkles className="h-5 w-5" />
                    <span>数据清理</span>
                  </CardTitle>
                  <CardDescription>
                    强制清理所有重复的知识库和文档数据
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">清理重复数据</h4>
                    <p className="text-sm text-muted-foreground">
                      扫描并删除重复的知识库和文档，只保留最早创建的版本
                    </p>
                                    <Button 
                  onClick={handleCleanupDuplicates} 
                  variant="outline"
                  disabled={isAnyOperationRunning}
                  className="w-full"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  强制清理重复数据
                </Button>
                  </div>
                </CardContent>
              </Card>

              {/* 示例数据管理 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>示例数据管理</span>
                  </CardTitle>
                  <CardDescription>
                    管理示例知识库和文档
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">重新初始化示例数据</h4>
                    <p className="text-sm text-muted-foreground">
                      重新创建咖啡文化完全指南等示例数据
                    </p>
                    <Button 
                      onClick={handleReinitialize} 
                      variant="outline"
                      disabled={isAnyOperationRunning}
                      className="w-full"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      重新初始化示例数据
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* 锁管理工具 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <RotateCcw className="h-5 w-5" />
                    <span>锁管理</span>
                  </CardTitle>
                  <CardDescription>
                    检查和管理示例数据初始化锁
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">初始化锁状态</h4>
                    <p className="text-sm text-muted-foreground">
                      检查是否有卡住的初始化锁，并可以手动清除
                    </p>
                    
                    {lockStatus && (
                      <div className="bg-muted p-3 rounded text-sm">
                        <div><strong>锁状态:</strong> {lockStatus.hasLock ? '已锁定' : '未锁定'}</div>
                        {lockStatus.hasLock && (
                          <>
                            <div><strong>锁定时间:</strong> {lockStatus.lockTime}</div>
                            <div><strong>是否过期:</strong> {lockStatus.isExpired ? '是' : '否'}</div>
                          </>
                        )}
                      </div>
                    )}
                    
                    <div className="flex space-x-2">
                      <Button 
                        onClick={handleCheckLock} 
                        variant="outline"
                        disabled={isAnyOperationRunning}
                        size="sm"
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        检查锁状态
                      </Button>
                      <Button 
                        onClick={handleClearLock} 
                        variant="outline"
                        disabled={isAnyOperationRunning || !lockStatus?.hasLock}
                        size="sm"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        清除锁
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 性能监控 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5" />
                    <span>性能监控</span>
                  </CardTitle>
                  <CardDescription>
                    实时监控消息更新和解析缓存性能
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PerformanceMonitor />
                </CardContent>
              </Card>

              {/* Google AI Provider 测试 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5" />
                    <span>Google AI 测试</span>
                  </CardTitle>
                  <CardDescription>
                    测试Google AI Provider的流式响应功能
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">流式响应测试</h4>
                    <p className="text-sm text-muted-foreground">
                      测试Google AI Provider的连接和流式响应功能
                    </p>
                    
                    <Button 
                      onClick={handleTestGoogleAI} 
                      variant="outline"
                      disabled={isTestingGoogle}
                      className="w-full"
                    >
                      {isTestingGoogle ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Settings className="h-4 w-4 mr-2" />
                      )}
                      {isTestingGoogle ? '测试中...' : '测试Google AI Provider'}
                    </Button>
                    
                    {googleTestResult && (
                      <div className="bg-muted p-3 rounded text-sm max-h-32 overflow-y-auto">
                        <div className="whitespace-pre-wrap">{googleTestResult}</div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* 完全重置 */}
              <Card className="md:col-span-2 lg:col-span-3">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-destructive">
                    <Trash2 className="h-5 w-5" />
                    <span>危险操作</span>
                  </CardTitle>
                  <CardDescription>
                    完全重置所有数据 - 不可逆操作
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert className="border-destructive/50 text-destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>警告：</strong>完全重置将删除所有数据库表、文件和配置。这个操作不可撤销
                    </AlertDescription>
                  </Alert>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium text-destructive">完全重置所有数据</h4>
                    <p className="text-sm text-muted-foreground">
                      将删除：数据库中的所有表和数据、本地存储、文件系统中的所有文档、应用配置
                    </p>
                    <Button 
                      onClick={handleFullReset} 
                      variant="destructive"
                      disabled={isAnyOperationRunning}
                      className="w-full"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      完全重置所有数据
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
} 