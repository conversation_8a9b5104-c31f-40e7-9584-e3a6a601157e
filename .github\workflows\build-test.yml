name: Build and Test

on:
  workflow_dispatch:
    inputs:
      action:
        description: 'Select action: "test" for checks, "build" for artifacts.'
        required: true
        default: 'test'
        type: choice
        options:
        - test
        - build
      platforms:
        description: 'Select platforms (only for "build" action).'
        required: false
        type: choice
        default: 'all'
        options:
        - all
        - ubuntu
        - windows
        - macos
        - ubuntu-windows
        - ubuntu-macos
        - windows-macos
      remark:
        description: 'Optional remark information.'
        required: false
        type: string
        default: ''

jobs:
  test:
    if: ${{ github.event.inputs.action == 'test' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # ✅ Cache Rust dependencies to speed up the build
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install Rust system dependencies
        run: sudo apt-get update && sudo apt-get install -y libwebkit2gtk-4.0-dev build-essential curl wget file libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Run linting
        run: pnpm lint
      - name: Type check
        run: pnpm tsc --noEmit
      - name: Build Frontend
        run: pnpm build
      - name: Test Tauri build
        run: pnpm tauri build --debug

  build-ubuntu:
    if: contains(github.event.inputs.action, 'build') && (contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'ubuntu'))
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # ✅ Cache Rust dependencies to speed up the build
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install Rust system dependencies
        run: sudo apt-get update && sudo apt-get install -y libwebkit2gtk-4.0-dev build-essential curl wget file libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true
      - name: Build Tauri app
        run: pnpm tauri build
      - name: Upload build artifacts (Linux)
        uses: actions/upload-artifact@v4
        with:
          name: chatless-ubuntu
          path: |
            src-tauri/target/release/bundle/appimage/*.AppImage
            src-tauri/target/release/bundle/deb/*.deb
          retention-days: 14

  build-windows:
    if: contains(github.event.inputs.action, 'build') && (contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'windows'))
    runs-on: windows-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # ✅ Cache Rust dependencies to speed up the build
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true
      - name: Build Tauri app
        run: pnpm tauri build
      - name: Upload build artifacts (Windows)
        uses: actions/upload-artifact@v4
        with:
          name: chatless-windows
          path: |
            src-tauri/target/release/bundle/msi/*.msi
            src-tauri/target/release/bundle/nsis/*.exe.zip
          retention-days: 14

  build-macos:
    if: contains(github.event.inputs.action, 'build') && (contains(github.event.inputs.platforms, 'all') || contains(github.event.inputs.platforms, 'macos'))
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # ✅ Cache Rust dependencies to speed up the build
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true
      - name: Build Tauri app
        run: pnpm tauri build
      - name: Upload build artifacts (macOS)
        uses: actions/upload-artifact@v4
        with:
          name: chatless-macos
          path: src-tauri/target/release/bundle/dmg/*.dmg
          retention-days: 14
