# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/
/dist/
/target/
/debug/
/rust-analyzer/
/release/

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo

# temporary files and scripts
test-*.js
test-*.ts
temp-*.md
*_TEMP.*
*_temp.*
*.tmp

# development notes and documentation
*_NOTES.md
*_FIXES.md
*_TROUBLESHOOTING.md
*_STATUS.md
*_FEATURES.md
*_MANAGEMENT.md
*_IMPLEMENTATION.md

.devdbrc

.cursor
.cursor/*
.cursorrules
.cursor/**/*.mdc