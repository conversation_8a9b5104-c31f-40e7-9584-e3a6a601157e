{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"useHookAtTopLevel": "error"}, "nursery": {"useGoogleFontDisplay": "error", "noDocumentImportInPage": "error", "noHeadElement": "error", "noHeadImportInDocument": "error", "noImgElement": "off", "useComponentExportOnlyModules": {"level": "error", "options": {"allowExportNames": ["metadata"]}}}}}, "css": {"formatter": {"quoteStyle": "double"}}, "javascript": {"formatter": {"quoteStyle": "double"}, "globals": []}}